import React from 'react'
import { Task, Message } from './api'

// 基础组件Props
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

// 按钮组件Props
export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
}

// 输入框组件Props
export interface InputProps extends BaseComponentProps {
  type?: 'text' | 'email' | 'password' | 'search'
  placeholder?: string
  value?: string
  defaultValue?: string
  disabled?: boolean
  required?: boolean
  onChange?: (value: string) => void
  onFocus?: () => void
  onBlur?: () => void
}

// 模态框组件Props
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  closeOnOverlayClick?: boolean
  closeOnEscape?: boolean
}

// 加载状态组件Props
export interface LoadingProps extends BaseComponentProps {
  message?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'spinner' | 'dots' | 'pulse'
}

// 任务相关组件Props
export interface TaskListProps extends BaseComponentProps {
  tasks: Task[]
  currentTaskId?: string
  onTaskSelect: (taskId: string) => void
  loading?: boolean
  error?: string | null
}

export interface TaskItemProps extends BaseComponentProps {
  task: Task
  isActive?: boolean
  onClick: (taskId: string) => void
  onDelete?: (taskId: string) => void
}

export interface TaskSearchProps extends BaseComponentProps {
  value?: string
  placeholder?: string
  onSearch: (query: string) => void
  onClear?: () => void
}

// 消息相关组件Props
export interface MessageListProps extends BaseComponentProps {
  messages: Message[]
  loading?: boolean
  onRetry?: (messageId: string) => void
}

export interface MessageItemProps extends BaseComponentProps {
  message: Message
  onRetry?: (messageId: string) => void
}

export interface ChatInputProps extends BaseComponentProps {
  onSend: (content: string, contextHtml?: string) => void
  disabled?: boolean
  placeholder?: string
  selectedContext?: string
  onContextClear?: () => void
}

// 报告相关组件Props
export interface ReportViewerProps extends BaseComponentProps {
  html: string
  loading?: boolean
  onAreaSelect?: (html: string) => void
  onSelectionClear?: () => void
}

export interface HTMLRendererProps extends BaseComponentProps {
  html: string
  onSelection?: () => void
}

export interface ReportToolbarProps extends BaseComponentProps {
  onExport: () => void
  onZoomIn: () => void
  onZoomOut: () => void
  onZoomReset: () => void
  zoomLevel: number
  viewMode: 'fit' | 'width' | 'actual'
  onViewModeChange: (mode: 'fit' | 'width' | 'actual') => void
}

// 布局相关组件Props
export interface MainLayoutProps extends BaseComponentProps {
  // 主布局组件通常不需要额外props
}

export interface SidebarPanelProps extends BaseComponentProps {
  collapsed?: boolean
  onToggle?: () => void
  width?: number
}

export interface ChatPanelProps extends BaseComponentProps {
  onResize?: (width: number) => void
}

export interface ReportViewerPanelProps extends BaseComponentProps {
  // 报告查看面板的特定props
}

// 响应式相关类型
export type Breakpoint = 'mobile' | 'tablet' | 'desktop'

export interface ResponsiveContainerProps extends BaseComponentProps {
  breakpoint?: Breakpoint
}

// 移动端组件Props
export interface MobileHeaderProps extends BaseComponentProps {
  title: string
  onMenuClick?: () => void
  onSettingsClick?: () => void
}

export interface MobileBottomNavProps extends BaseComponentProps {
  activePanel: 'sidebar' | 'report' | 'chat'
  onPanelChange: (panel: 'sidebar' | 'report' | 'chat') => void
}

export interface SwipeablePanelProps extends BaseComponentProps {
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
}

// 事件处理器类型
export type EventHandler<T = void> = (event?: React.SyntheticEvent) => T
export type ChangeHandler<T> = (value: T) => void
export type ClickHandler = EventHandler<void>

// 通用工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredProps<T, K extends keyof T> = T & Required<Pick<T, K>>

// 组件状态类型
export interface ComponentState {
  loading: boolean
  error: string | null
  data: any
}

// 表单相关类型
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'textarea' | 'select'
  required?: boolean
  placeholder?: string
  options?: Array<{ label: string; value: string }>
}

export interface FormProps extends BaseComponentProps {
  fields: FormField[]
  onSubmit: (data: Record<string, any>) => void
  loading?: boolean
  initialValues?: Record<string, any>
}
