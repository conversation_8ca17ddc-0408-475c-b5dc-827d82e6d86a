import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { webSocketManager } from '../services'
import { messageService } from '../services/api/messageService'
import type { ChatStore } from '../types/store'
import type { Message } from '../types/api'

// 生成唯一ID的工具函数
const generateId = () => `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

// 全局WebSocket监听器设置（只设置一次）
let isWebSocketListenersSetup = false

export const useChatStore = create<ChatStore>()(
  devtools(
    immer((set, get) => {
      // 只在第一次创建Store时设置监听器
      if (!isWebSocketListenersSetup) {
        isWebSocketListenersSetup = true

        // 设置WebSocket消息监听器
        webSocketManager.onMessage((message) => {
          const state = get()

          switch (message.type) {
            case 'assistant_message_chunk':
              if (state.currentResponseId) {
                state.appendResponseChunk(state.currentResponseId, message.content)
              } else {
                const responseId = generateId()
                state.startResponse(responseId)
                state.appendResponseChunk(responseId, message.content)
              }
              break

            case 'code_snippet_start':
              // 代码片段开始，通知报告开始生成
              console.log('Code snippet generation started')
              // 这里可以通过事件或直接调用reportStore来通知开始生成
              window.dispatchEvent(new CustomEvent('reportGenerationStart'))
              break

            case 'code_snippet_chunk':
              // 代码片段内容，累积到报告中
              console.log('Code snippet chunk received:', message.content)
              window.dispatchEvent(new CustomEvent('reportGenerationChunk', {
                detail: { content: message.content }
              }))
              break

            case 'code_snippet_end':
              // 代码片段结束，完成报告生成
              console.log('Code snippet generation ended, report path:', message.report_file_path)
              window.dispatchEvent(new CustomEvent('reportGenerationEnd', {
                detail: { reportPath: message.report_file_path }
              }))

              if (state.currentResponseId) {
                state.finishResponse(state.currentResponseId)
              }
              break

            case 'error':
              set((state) => {
                state.error = message.content
                state.isTyping = false
                state.isReceivingResponse = false
              })
              break

            default:
              console.warn('Unknown WebSocket message type:', message)
          }
        })

        // 监听连接状态变化
        webSocketManager.onConnectionChange((status) => {
          set((state) => {
            state.connectionStatus = status
            state.isConnected = status === 'connected'
          })
        })
      }

      return {
        // 初始状态
        messages: [],
        isConnected: false,
        connectionStatus: 'disconnected',
        isTyping: false,
        inputContent: '',
        selectedContext: null,
        isReceivingResponse: false,
        currentResponseId: null,
        error: null,

      // 发送消息
      sendMessage: (content, contextHtml) => {
        const userMessage: Message = {
          id: generateId(),
          role: 'user',
          content,
          timestamp: new Date().toISOString(),
          has_code_snippet: false
        }

        set((state) => {
          state.messages.push(userMessage)
          state.inputContent = ''
          state.selectedContext = null
          state.isTyping = true
        })

        // 通过WebSocket发送消息
        webSocketManager.sendMessage({
          type: 'user_message',
          content,
          context_html: contextHtml || ''
        })
      },

      // 添加消息
      addMessage: (message) => {
        set((state) => {
          state.messages.push(message)
        })
      },

      // 更新消息
      updateMessage: (messageId, updates) => {
        set((state) => {
          const index = state.messages.findIndex(m => m.id === messageId)
          if (index !== -1) {
            state.messages[index] = { ...state.messages[index], ...updates }
          }
        })
      },

      // 删除消息
      deleteMessage: (messageId) => {
        set((state) => {
          state.messages = state.messages.filter(m => m.id !== messageId)
        })
      },

      // WebSocket连接
      connect: async (taskId) => {
        console.log(`[ChatStore] Connecting to task: ${taskId}`)

        // 清理之前的聊天数据
        set((state) => {
          console.log(`[ChatStore] Clearing previous messages, had ${state.messages.length} messages`)
          state.messages = []
          state.connectionStatus = 'connecting'
          state.error = null
          state.isTyping = false
          state.isReceivingResponse = false
          state.currentResponseId = null
          state.selectedContext = null
        })

        // 先断开之前的连接，避免重复监听
        webSocketManager.disconnect()

        try {
          // 先加载历史消息
          console.log(`[ChatStore] Loading task messages for: ${taskId}`)
          await get().loadTaskMessages(taskId)

          // 检查消息是否已加载
          const currentMessages = get().messages
          console.log(`[ChatStore] After loading messages, store has ${currentMessages.length} messages`)

          // 然后连接WebSocket
          console.log(`[ChatStore] Connecting WebSocket for: ${taskId}`)
          await webSocketManager.connect(taskId)

          set((state) => {
            state.isConnected = true
            state.connectionStatus = 'connected'
          })

          console.log(`[ChatStore] Successfully connected to task: ${taskId}`)
        } catch (error) {
          console.error(`[ChatStore] Failed to connect to task ${taskId}:`, error)
          set((state) => {
            state.isConnected = false
            state.connectionStatus = 'error'
            state.error = error instanceof Error ? error.message : 'Connection failed'
          })
        }
      },

      // 加载任务的历史消息
      loadTaskMessages: async (taskId) => {
        try {
          console.log(`Loading messages for task: ${taskId}`)
          const messages = await messageService.getTaskMessages(taskId)
          console.log(`Loaded ${messages.length} messages for task: ${taskId}`, messages)

          // 确保消息按时间排序
          const sortedMessages = messages.sort((a, b) =>
            new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          )

          set((state) => {
            state.messages = sortedMessages
          })

          console.log(`Set ${sortedMessages.length} messages in store`)

          // 如果没有历史消息，添加欢迎消息
          if (sortedMessages.length === 0) {
            const welcomeMessage: Message = {
              id: `welcome_${taskId}`,
              role: 'system',
              content: `欢迎！您已连接到任务: ${taskId}。请开始对话。`,
              timestamp: new Date().toISOString(),
              has_code_snippet: false
            }

            set((state) => {
              state.messages = [welcomeMessage]
            })
            console.log('Added welcome message')
          }
        } catch (error) {
          console.error('Failed to load task messages:', error)
          // 如果加载失败，显示系统消息
          const systemMessage: Message = {
            id: `system_${taskId}`,
            role: 'system',
            content: `连接失败，请重试。任务ID: ${taskId}`,
            timestamp: new Date().toISOString(),
            has_code_snippet: false
          }

          set((state) => {
            state.messages = [systemMessage]
          })
          console.log('Added error message')
        }
      },

      // 断开连接
      disconnect: () => {
        webSocketManager.disconnect()
        set((state) => {
          state.isConnected = false
          state.connectionStatus = 'disconnected'
        })
      },

      // 清理聊天数据
      clearMessages: () => {
        set((state) => {
          state.messages = []
          state.isTyping = false
          state.isReceivingResponse = false
          state.currentResponseId = null
          state.selectedContext = null
          state.error = null
        })
      },

      // 重连
      reconnect: () => {
        webSocketManager.reconnect()
          .catch((error) => {
            set((state) => {
              state.error = error.message
            })
          })
      },

      // 设置输入内容
      setInputContent: (content) => {
        set((state) => {
          state.inputContent = content
        })
      },

      // 设置选中的上下文
      setSelectedContext: (context) => {
        set((state) => {
          state.selectedContext = context
        })
      },

      // 开始流式响应
      startResponse: (responseId) => {
        set((state) => {
          state.isReceivingResponse = true
          state.currentResponseId = responseId
          state.isTyping = false
        })

        // 添加助手消息占位符
        const assistantMessage: Message = {
          id: responseId,
          role: 'assistant',
          content: '',
          timestamp: new Date().toISOString(),
          has_code_snippet: false
        }

        get().addMessage(assistantMessage)
      },

      // 追加响应片段
      appendResponseChunk: (responseId, chunk) => {
        set((state) => {
          const messageIndex = state.messages.findIndex(m => m.id === responseId)
          if (messageIndex !== -1) {
            state.messages[messageIndex].content += chunk
          }
        })
      },

      // 完成响应
      finishResponse: (responseId) => {
        set((state) => {
          state.isReceivingResponse = false
          state.currentResponseId = null
          
          // 标记消息包含代码片段
          const messageIndex = state.messages.findIndex(m => m.id === responseId)
          if (messageIndex !== -1) {
            state.messages[messageIndex].has_code_snippet = true
          }
        })
      },

      // 加载消息
      loadMessages: (messages) => {
        set((state) => {
          state.messages = messages
        })
      },



      // 重置状态
      reset: () => {
        set((state) => {
          state.messages = []
          state.inputContent = ''
          state.selectedContext = null
          state.isReceivingResponse = false
          state.currentResponseId = null
          state.error = null
        })

        get().disconnect()
      }
      }
    }),
    { name: 'ChatStore' }
  )
)
