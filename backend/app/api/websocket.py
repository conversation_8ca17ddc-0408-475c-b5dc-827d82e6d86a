"""
WebSocket流式对话处理
"""
import json
import re
from fastapi import APIRouter, WebSocket, WebSocketDisconnect

from app.services.task_service import get_or_create_task, save_report_file
from app.services.message_service import save_message
from app.services.llm_service import generate_llm_response


websocket_router = APIRouter()


def extract_code_snippet(text: str) -> tuple[str, str]:
    """提取代码片段
    
    Returns:
        tuple: (clean_text, code_snippet)
    """
    pattern = r'<code_snippet>(.*?)</code_snippet>'
    match = re.search(pattern, text, re.DOTALL)
    
    if match:
        code_snippet = match.group(1).strip()
        clean_text = re.sub(pattern, '', text, flags=re.DOTALL).strip()
        return clean_text, code_snippet
    
    return text, ""


@websocket_router.websocket("/ws/chat/{task_id}")
async def websocket_chat(websocket: WebSocket, task_id: str):
    """WebSocket聊天端点"""
    await websocket.accept()
    
    try:
        # 确保任务存在
        task = await get_or_create_task(task_id)
        
        while True:
            # 接收用户消息
            try:
                data = await websocket.receive_text()
                message_data = json.loads(data)
            except json.JSONDecodeError:
                await websocket.send_json({
                    "type": "error",
                    "content": "Invalid JSON format"
                })
                continue
            
            user_message = message_data.get("content", "")
            context_html = message_data.get("context_html", "")
            
            if not user_message.strip():
                continue
            
            # 保存用户消息
            await save_message(task_id, "user", user_message)
            
            # 生成LLM响应
            full_response = ""
            text_response = ""  # 只包含文本部分的响应
            code_snippet = ""
            in_code_snippet = False
            current_code_buffer = ""
            has_code_snippet = False
            
            try:
                async for chunk in generate_llm_response(user_message, context_html):
                    full_response += chunk

                    # 检查是否开始代码片段
                    if "<code_snippet>" in chunk and not in_code_snippet:
                        in_code_snippet = True
                        has_code_snippet = True

                        # 处理代码片段前的文本
                        before_code = chunk[:chunk.find("<code_snippet>")]
                        if before_code.strip():
                            text_response += before_code
                            await websocket.send_json({
                                "type": "assistant_message_chunk",
                                "content": before_code
                            })

                        # 发送代码片段开始信号
                        await websocket.send_json({
                            "type": "code_snippet_start",
                            "content": ""
                        })

                        # 开始收集代码内容
                        start_idx = chunk.find("<code_snippet>") + len("<code_snippet>")
                        if start_idx < len(chunk):
                            current_code_buffer += chunk[start_idx:]

                        # 检查是否在同一chunk中结束
                        if "</code_snippet>" in chunk:
                            end_idx = current_code_buffer.find("</code_snippet>")
                            if end_idx != -1:
                                actual_code = current_code_buffer[:end_idx]
                                code_snippet = actual_code

                                # 保存报告文件并发送结束信号
                                try:
                                    report_path = await save_report_file(task_id, actual_code)
                                    await websocket.send_json({
                                        "type": "code_snippet_end",
                                        "content": "",
                                        "report_file_path": report_path
                                    })
                                except Exception as e:
                                    await websocket.send_json({
                                        "type": "error",
                                        "content": f"Failed to save report: {str(e)}"
                                    })

                                in_code_snippet = False
                                current_code_buffer = ""

                                # 处理代码片段后的文本
                                remaining_text = chunk[chunk.find("</code_snippet>") + len("</code_snippet>"):]
                                if remaining_text.strip():
                                    text_response += remaining_text
                                    await websocket.send_json({
                                        "type": "assistant_message_chunk",
                                        "content": remaining_text
                                    })

                    elif in_code_snippet:
                        # 在代码片段中，只累积代码内容，不发送任何消息
                        current_code_buffer += chunk

                        # 检查是否结束代码片段
                        if "</code_snippet>" in chunk:
                            end_idx = current_code_buffer.find("</code_snippet>")
                            if end_idx != -1:
                                actual_code = current_code_buffer[:end_idx]
                                code_snippet = actual_code

                                # 保存报告文件并发送结束信号
                                try:
                                    report_path = await save_report_file(task_id, actual_code)
                                    await websocket.send_json({
                                        "type": "code_snippet_end",
                                        "content": "",
                                        "report_file_path": report_path
                                    })
                                except Exception as e:
                                    await websocket.send_json({
                                        "type": "error",
                                        "content": f"Failed to save report: {str(e)}"
                                    })

                                in_code_snippet = False
                                current_code_buffer = ""

                                # 处理代码片段后的文本
                                remaining_text = chunk[chunk.find("</code_snippet>") + len("</code_snippet>"):]
                                if remaining_text.strip():
                                    text_response += remaining_text
                                    await websocket.send_json({
                                        "type": "assistant_message_chunk",
                                        "content": remaining_text
                                    })

                    else:
                        # 普通文本消息
                        text_response += chunk
                        await websocket.send_json({
                            "type": "assistant_message_chunk",
                            "content": chunk
                        })
                
            except Exception as e:
                await websocket.send_json({
                    "type": "error",
                    "content": f"LLM调用失败: {str(e)}"
                })

            # 保存助手的文本响应（无论是否有异常都要保存）
            try:
                message_content = text_response.strip() if text_response.strip() else "生成了报告内容"
                print(f"Saving assistant message for task {task_id}: {message_content[:50]}... (has_code: {has_code_snippet})")

                await save_message(
                    task_id,
                    "assistant",
                    message_content,
                    has_code_snippet
                )
                print(f"Assistant message saved successfully for task {task_id}")
            except Exception as e:
                print(f"Failed to save assistant message for task {task_id}: {e}")
    
    except WebSocketDisconnect:
        print(f"WebSocket disconnected for task {task_id}")
    except Exception as e:
        print(f"WebSocket error for task {task_id}: {str(e)}")
        try:
            await websocket.send_json({
                "type": "error",
                "content": f"服务器错误: {str(e)}"
            })
        except:
            pass
